{"name": "vanilla-calendar-pro", "description": "The Vanilla Calendar Pro is a versatile JavaScript date and time picker component with TypeScript support, making it compatible with any JavaScript frameworks and libraries. It is designed to be lightweight, easy to use, and feature-rich, without relying on external dependencies.", "version": "3.0.4", "private": false, "homepage": "https://vanilla-calendar.pro", "keywords": ["calendar", "datepicker", "timepicker", "date-picker", "vanilla-js", "javascript", "typescript", "react", "native", "pure", "picker", "vanilla", "default", "js", "ts"], "repository": {"type": "git", "url": "https://github.com/uvarov-frontend/vanilla-calendar-pro.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://frontend.uvarov.tech"}, "bugs": {"url": "https://github.com/uvarov-frontend/vanilla-calendar-pro/issues", "email": "<EMAIL>"}, "funding": {"type": "individual", "url": "https://buymeacoffee.com/uvarov"}, "license": "MIT", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "style": "./styles/index.css", "exports": {".": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.mjs", "default": "./index.mjs"}, "./utils": {"types": "./utils/index.d.ts", "require": "./utils/index.js", "import": "./utils/index.mjs", "default": "./utils/index.mjs"}, "./styles/*": "./styles/*", "./package.json": "./package.json"}, "devDependencies": {}, "dependencies": {}}