/*! name: vanilla-calendar-pro v3.0.4 | url: https://github.com/uvarov-frontend/vanilla-calendar-pro */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).VanillaCalendarPro={})}(this,(function(e){"use strict";var t=Object.defineProperty,n=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,r=(e,n,a)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[n]=a,i=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&r(e,n,t[n]);if(o)for(var n of o(t))s.call(t,n)&&r(e,n,t[n]);return e},c=(e,t,n)=>(r(e,"symbol"!=typeof t?t+"":t,n),n);const d=e=>`${e} is not found, check the first argument passed to new Calendar.`,u='The calendar has not been initialized, please initialize it using the "init()" method first.',m="You specified an incorrect language label or did not specify the required number of values ​​for «locale.weekdays» or «locale.months».",h="The value of the time property can be: false, 12 or 24.",v="For the «multiple» calendar type, the «displayMonthsCount» parameter can have a value from 2 to 12, and for all others it cannot be greater than 1.",p=(e,t,n)=>{e.context[t]=n},y=e=>{e.context.isShowInInputMode&&e.context.currentType&&(e.context.mainElement.dataset.vcCalendarHidden="",p(e,"isShowInInputMode",!1),e.context.cleanupHandlers[0]&&(e.context.cleanupHandlers.forEach((e=>e())),p(e,"cleanupHandlers",[])),e.onHide&&e.onHide(e))};function x(e){if(!e||!e.getBoundingClientRect)return{top:0,bottom:0,left:0,right:0};const t=e.getBoundingClientRect(),n=document.documentElement;return{bottom:t.bottom,right:t.right,top:t.top+window.scrollY-n.clientTop,left:t.left+window.scrollX-n.clientLeft}}function g(){return{vw:Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),vh:Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)}}function b(e){const{top:t,left:n}={left:window.scrollX||document.documentElement.scrollLeft||0,top:window.scrollY||document.documentElement.scrollTop||0},{top:a,left:o}=x(e),{vh:l,vw:s}=g(),r=a-t,i=o-n;return{top:r,bottom:l-(r+e.clientHeight),left:i,right:s-(i+e.clientWidth)}}function M(e,t,n=5){const a={top:!0,bottom:!0,left:!0,right:!0},o=[];if(!t||!e)return{canShow:a,parentPositions:o};const{bottom:l,top:s}=b(e),{top:r,left:i}=x(e),{height:c,width:d}=t.getBoundingClientRect(),{vh:u,vw:m}=g(),h=m/2,v=u/2;return[{condition:r<v,position:"top"},{condition:r>v,position:"bottom"},{condition:i<h,position:"left"},{condition:i>h,position:"right"}].forEach((({condition:e,position:t})=>{e&&o.push(t)})),Object.assign(a,{top:c<=s-n,bottom:c<=l-n,left:d<=i,right:d<=m-i}),{canShow:a,parentPositions:o}}const f=(e,t)=>{var n;e.popups&&(null==(n=Object.entries(e.popups))||n.forEach((([n,a])=>((e,t,n,a)=>{var o;const l=a.querySelector(`[data-vc-date="${t}"]`),s=null==l?void 0:l.querySelector("[data-vc-date-btn]");if(!l||!s)return;if((null==n?void 0:n.modifier)&&s.classList.add(...n.modifier.trim().split(" ")),!(null==n?void 0:n.html))return;const r=document.createElement("div");r.className=e.styles.datePopup,r.dataset.vcDatePopup="",r.innerHTML=e.sanitizerHTML(n.html),s.ariaExpanded="true",s.ariaLabel=`${s.ariaLabel}, ${null==(o=null==r?void 0:r.textContent)?void 0:o.replace(/^\s+|\s+(?=\s)|\s+$/g,"").replace(/&nbsp;/g," ")}`,l.appendChild(r),requestAnimationFrame((()=>{if(!r)return;const{canShow:e}=M(l,r),t=e.bottom?l.offsetHeight:-r.offsetHeight,n=e.left&&!e.right?l.offsetWidth-r.offsetWidth/2:!e.left&&e.right?r.offsetWidth/2:0;Object.assign(r.style,{left:`${n}px`,top:`${t}px`})}))})(e,n,a,t))))},D=e=>new Date(`${e}T00:00:00`),E=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,w=e=>e.reduce(((e,t)=>{if(t instanceof Date||"number"==typeof t){const n=t instanceof Date?t:new Date(t);e.push(n.toISOString().substring(0,10))}else t.match(/^(\d{4}-\d{2}-\d{2})$/g)?e.push(t):t.replace(/(\d{4}-\d{2}-\d{2}).*?(\d{4}-\d{2}-\d{2})/g,((t,n,a)=>{const o=D(n),l=D(a),s=new Date(o.getTime());for(;s<=l;s.setDate(s.getDate()+1))e.push(E(s));return t}));return e}),[]),T=(e,t,n,a="")=>{t?e.setAttribute(n,a):e.getAttribute(n)===a&&e.removeAttribute(n)},k=(e,t,n,a,o,l,s)=>{var r,i,c,d;const u=D(e.context.displayDateMin)>D(l)||D(e.context.displayDateMax)<D(l)||(null==(r=e.context.disableDates)?void 0:r.includes(l))||!e.selectionMonthsMode&&"current"!==s||!e.selectionYearsMode&&D(l).getFullYear()!==t;T(n,u,"data-vc-date-disabled"),a&&T(a,u,"aria-disabled","true"),a&&T(a,u,"tabindex","-1"),T(n,!e.disableToday&&e.context.dateToday===l,"data-vc-date-today"),T(n,!e.disableToday&&e.context.dateToday===l,"aria-current","date"),T(n,null==(i=e.selectedWeekends)?void 0:i.includes(o),"data-vc-date-weekend");const m=(null==(c=e.selectedHolidays)?void 0:c[0])?w(e.selectedHolidays):[];if(T(n,m.includes(l),"data-vc-date-holiday"),(null==(d=e.context.selectedDates)?void 0:d.includes(l))?(n.setAttribute("data-vc-date-selected",""),a&&a.setAttribute("aria-selected","true"),e.context.selectedDates.length>1&&"multiple-ranged"===e.selectionDatesMode&&(e.context.selectedDates[0]===l&&e.context.selectedDates[e.context.selectedDates.length-1]===l?n.setAttribute("data-vc-date-selected","first-and-last"):e.context.selectedDates[0]===l?n.setAttribute("data-vc-date-selected","first"):e.context.selectedDates[e.context.selectedDates.length-1]===l&&n.setAttribute("data-vc-date-selected","last"),e.context.selectedDates[0]!==l&&e.context.selectedDates[e.context.selectedDates.length-1]!==l&&n.setAttribute("data-vc-date-selected","middle"))):n.hasAttribute("data-vc-date-selected")&&(n.removeAttribute("data-vc-date-selected"),a&&a.removeAttribute("aria-selected")),!e.context.disableDates.includes(l)&&e.enableEdgeDatesOnly&&e.context.selectedDates.length>1&&"multiple-ranged"===e.selectionDatesMode){const t=D(e.context.selectedDates[0]),a=D(e.context.selectedDates[e.context.selectedDates.length-1]),o=D(l);T(n,o>t&&o<a,"data-vc-date-selected","middle")}},$=(e,t)=>{const n=D(e),a=(n.getDay()-t+7)%7;n.setDate(n.getDate()+4-a);const o=new Date(n.getFullYear(),0,1),l=Math.ceil(((+n-+o)/864e5+1)/7);return{year:n.getFullYear(),week:l}},A=(e,t,n,a,o,l)=>{const s=D(o).getDay(),r="string"==typeof e.locale&&e.locale.length?e.locale:"en",i=document.createElement("div");let c;i.className=e.styles.date,i.dataset.vcDate=o,i.dataset.vcDateMonth=l,i.dataset.vcDateWeekDay=String(s),("current"===l||e.displayDatesOutside)&&(c=document.createElement("button"),c.className=e.styles.dateBtn,c.type="button",c.role="gridcell",c.ariaLabel=((e,t,n)=>new Date(`${e}T00:00:00.000Z`).toLocaleString(t,n))(o,r,{dateStyle:"long",timeZone:"UTC"}),c.dataset.vcDateBtn="",c.innerText=String(a),i.appendChild(c)),e.enableWeekNumbers&&((e,t,n)=>{const a=$(n,e.firstWeekday);a&&(t.dataset.vcDateWeekNumber=String(a.week))})(e,i,o),((e,t,n)=>{var a,o,l,s,r;const i=null==(a=e.disableWeekdays)?void 0:a.includes(n),c=e.disableAllDates&&!!(null==(o=e.context.enableDates)?void 0:o[0]);!i&&!c||(null==(l=e.context.enableDates)?void 0:l.includes(t))||(null==(s=e.context.disableDates)?void 0:s.includes(t))||(e.context.disableDates.push(t),null==(r=e.context.disableDates)||r.sort(((e,t)=>+new Date(e)-+new Date(t))))})(e,o,s),k(e,t,i,c,s,o,l),n.appendChild(i),e.onCreateDateEls&&e.onCreateDateEls(e,i)},C=e=>{const t=new Date(e.context.selectedYear,e.context.selectedMonth,1),n=e.context.mainElement.querySelectorAll('[data-vc="dates"]'),a=e.context.mainElement.querySelectorAll('[data-vc-week="numbers"]');n.forEach(((n,o)=>{e.selectionDatesMode||(n.dataset.vcDatesDisabled=""),n.textContent="";const l=new Date(t);l.setMonth(l.getMonth()+o);const s=l.getMonth(),r=l.getFullYear(),i=(new Date(r,s,1).getDay()-e.firstWeekday+7)%7,c=new Date(r,s+1,0).getDate();((e,t,n,a,o)=>{let l=new Date(n,a,0).getDate()-(o-1);const s=0===a?n-1:n,r=0===a?12:a<10?`0${a}`:a;for(let a=o;a>0;a--,l++)A(e,n,t,l,`${s}-${r}-${l}`,"prev")})(e,n,r,s,i),((e,t,n,a,o)=>{for(let l=1;l<=n;l++){const n=new Date(a,o,l);A(e,a,t,l,E(n),"current")}})(e,n,c,r,s),((e,t,n,a,o,l)=>{const s=l+n,r=7*Math.ceil(s/7)-s,i=o+1===12?a+1:a,c=o+1===12?"01":o+2<10?`0${o+2}`:o+2;for(let n=1;n<=r;n++){const o=n<10?`0${n}`:String(n);A(e,a,t,n,`${i}-${c}-${o}`,"next")}})(e,n,c,r,s,i),f(e,n),((e,t,n,a,o)=>{if(!e.enableWeekNumbers)return;a.textContent="";const l=document.createElement("b");l.className=e.styles.weekNumbersTitle,l.innerText="#",l.dataset.vcWeekNumbers="title",a.appendChild(l);const s=document.createElement("div");s.className=e.styles.weekNumbersContent,s.dataset.vcWeekNumbers="content",a.appendChild(s);const r=document.createElement("button");r.type="button",r.className=e.styles.weekNumber;const i=o.querySelectorAll("[data-vc-date]"),c=Math.ceil((t+n)/7);for(let t=0;t<c;t++){const n=i[0===t?6:7*t].dataset.vcDate,a=$(n,e.firstWeekday);if(!a)return;const o=r.cloneNode(!0);o.innerText=String(a.week),o.dataset.vcWeekNumber=String(a.week),o.dataset.vcWeekYear=String(a.year),o.role="rowheader",o.ariaLabel=`${a.week}`,s.appendChild(o)}})(e,i,c,a[o],n)}))},S=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [month] />\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n    <#ArrowNext [month] />\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <#WeekNumbers />\n    <div class="${e.styles.content}" data-vc="content">\n      <#Week />\n      <#Dates />\n      <#DateRangeTooltip />\n    </div>\n  </div>\n  <#ControlTime />\n`,Y=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <div class="${e.styles.content}" data-vc="content">\n      <#Months />\n    </div>\n  </div>\n`,L=e=>`\n  <div class="${e.styles.controls}" data-vc="controls" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [month] />\n    <#ArrowNext [month] />\n  </div>\n  <div class="${e.styles.grid}" data-vc="grid">\n    <#Multiple>\n      <div class="${e.styles.column}" data-vc="column" role="region">\n        <div class="${e.styles.header}" data-vc="header">\n          <div class="${e.styles.headerContent}" data-vc-header="content">\n            <#Month />\n            <#Year />\n          </div>\n        </div>\n        <div class="${e.styles.wrapper}" data-vc="wrapper">\n          <#WeekNumbers />\n          <div class="${e.styles.content}" data-vc="content">\n            <#Week />\n            <#Dates />\n          </div>\n        </div>\n      </div>\n    <#/Multiple>\n    <#DateRangeTooltip />\n  </div>\n  <#ControlTime />\n`,N=e=>`\n  <div class="${e.styles.header}" data-vc="header" role="toolbar" aria-label="${e.labels.navigation}">\n    <#ArrowPrev [year] />\n    <div class="${e.styles.headerContent}" data-vc-header="content">\n      <#Month />\n      <#Year />\n    </div>\n    <#ArrowNext [year] />\n  </div>\n  <div class="${e.styles.wrapper}" data-vc="wrapper">\n    <div class="${e.styles.content}" data-vc="content">\n      <#Years />\n    </div>\n  </div>\n`,H={ArrowNext:(e,t)=>`<button type="button" class="${e.styles.arrowNext}" data-vc-arrow="next" aria-label="${e.labels.arrowNext[t]}"></button>`,ArrowPrev:(e,t)=>`<button type="button" class="${e.styles.arrowPrev}" data-vc-arrow="prev" aria-label="${e.labels.arrowPrev[t]}"></button>`,ControlTime:e=>e.selectionTimeMode?`<div class="${e.styles.time}" data-vc="time" role="group" aria-label="${e.labels.selectingTime}"></div>`:"",Dates:e=>`<div class="${e.styles.dates}" data-vc="dates" role="grid" aria-live="assertive" aria-label="${e.labels.dates}" ${"multiple"===e.type?"aria-multiselectable":""}></div>`,DateRangeTooltip:e=>e.onCreateDateRangeTooltip?`<div class="${e.styles.dateRangeTooltip}" data-vc-date-range-tooltip="hidden"></div>`:"",Month:e=>`<button type="button" class="${e.styles.month}" data-vc="month"></button>`,Months:e=>`<div class="${e.styles.months}" data-vc="months" role="grid" aria-live="assertive" aria-label="${e.labels.months}"></div>`,Week:e=>`<div class="${e.styles.week}" data-vc="week" role="row" aria-label="${e.labels.week}"></div>`,WeekNumbers:e=>e.enableWeekNumbers?`<div class="${e.styles.weekNumbers}" data-vc-week="numbers" role="row" aria-label="${e.labels.weekNumber}"></div>`:"",Year:e=>`<button type="button" class="${e.styles.year}" data-vc="year"></button>`,Years:e=>`<div class="${e.styles.years}" data-vc="years" role="grid" aria-live="assertive" aria-label="${e.labels.years}"></div>`},W=(e,t)=>t.replace(/[\n\t]/g,"").replace(/<#(?!\/?Multiple)(.*?)>/g,((t,n)=>{const a=(n.match(/\[(.*?)\]/)||[])[1],o=n.replace(/[/\s\n\t]|\[(.*?)\]/g,""),l=H[o];const s=l?l(e,null!=a?a:null):"";return e.sanitizerHTML(s)})).replace(/[\n\t]/g,""),q=(e,t)=>{const n={default:S,month:Y,year:N,multiple:L};if(Object.keys(n).forEach((t=>{const a=t;e.layouts[a].length||(e.layouts[a]=n[a](e))})),e.context.mainElement.className=e.styles.calendar,e.context.mainElement.dataset.vc="calendar",e.context.mainElement.dataset.vcType=e.context.currentType,e.context.mainElement.role="application",e.context.mainElement.tabIndex=0,e.context.mainElement.ariaLabel=e.labels.application,"multiple"!==e.context.currentType){if("multiple"===e.type&&t){const n=e.context.mainElement.querySelector('[data-vc="controls"]'),a=e.context.mainElement.querySelector('[data-vc="grid"]'),o=t.closest('[data-vc="column"]');return n&&e.context.mainElement.removeChild(n),a&&(a.dataset.vcGrid="hidden"),o&&(o.dataset.vcColumn=e.context.currentType),void(o&&(o.innerHTML=e.sanitizerHTML(W(e,e.layouts[e.context.currentType]))))}e.context.mainElement.innerHTML=e.sanitizerHTML(W(e,e.layouts[e.context.currentType]))}else e.context.mainElement.innerHTML=e.sanitizerHTML(((e,t)=>t.replace(new RegExp("<#Multiple>(.*?)<#\\/Multiple>","gs"),((t,n)=>{const a=Array(e.context.displayMonthsCount).fill(n).join("");return e.sanitizerHTML(a)})).replace(/[\n\t]/g,""))(e,W(e,e.layouts[e.context.currentType])))},P=(e,t,n,a)=>{e.style.visibility=n?"hidden":"",t.style.visibility=a?"hidden":""},I=e=>{if("month"===e.context.currentType)return;const t=e.context.mainElement.querySelector('[data-vc-arrow="prev"]'),n=e.context.mainElement.querySelector('[data-vc-arrow="next"]');if(!t||!n)return;const a={default:()=>((e,t,n)=>{const a=D(E(new Date(e.context.selectedYear,e.context.selectedMonth,1))),o=new Date(a.getTime()),l=new Date(a.getTime());o.setMonth(o.getMonth()-e.monthsToSwitch),l.setMonth(l.getMonth()+e.monthsToSwitch);const s=D(e.context.dateMin),r=D(e.context.dateMax);e.selectionYearsMode||(s.setFullYear(a.getFullYear()),r.setFullYear(a.getFullYear()));const i=!e.selectionMonthsMode||o.getFullYear()<s.getFullYear()||o.getFullYear()===s.getFullYear()&&o.getMonth()<s.getMonth(),c=!e.selectionMonthsMode||l.getFullYear()>r.getFullYear()||l.getFullYear()===r.getFullYear()&&l.getMonth()>r.getMonth()-(e.context.displayMonthsCount-1);P(t,n,i,c)})(e,t,n),year:()=>((e,t,n)=>{const a=D(e.context.dateMin),o=D(e.context.dateMax),l=!!(a.getFullYear()&&e.context.displayYear-7<=a.getFullYear()),s=!!(o.getFullYear()&&e.context.displayYear+7>=o.getFullYear());P(t,n,l,s)})(e,t,n)};a["multiple"===e.context.currentType?"default":e.context.currentType]()},F=e=>{const t=e.context.mainElement.querySelectorAll('[data-vc="month"]'),n=e.context.mainElement.querySelectorAll('[data-vc="year"]'),a=new Date(e.context.selectedYear,e.context.selectedMonth,1);[t,n].forEach((t=>null==t?void 0:t.forEach(((t,n)=>((e,t,n,a,o)=>{const l=new Date(a.setFullYear(e.context.selectedYear,e.context.selectedMonth+n)).getFullYear(),s=new Date(a.setMonth(e.context.selectedMonth+n)).getMonth(),r=e.context.locale.months.long[s],i=t.closest('[data-vc="column"]');i&&(i.ariaLabel=`${r} ${l}`);const c={month:{id:s,label:r},year:{id:l,label:l}};t.innerText=String(c[o].label),t.dataset[`vc${o.charAt(0).toUpperCase()+o.slice(1)}`]=String(c[o].id),t.ariaLabel=`${e.labels[o]} ${c[o].label}`;const d={month:e.selectionMonthsMode,year:e.selectionYearsMode},u=!1===d[o]||"only-arrows"===d[o];u&&(t.tabIndex=-1),t.disabled=u})(e,t,n,a,t.dataset.vc)))))},O=(e,t,n,a,o)=>{var l;const s={month:"[data-vc-months-month]",year:"[data-vc-years-year]"},r={month:{selected:"data-vc-months-month-selected",aria:"aria-selected",value:"vcMonthsMonth",selectedProperty:"selectedMonth"},year:{selected:"data-vc-years-year-selected",aria:"aria-selected",value:"vcYearsYear",selectedProperty:"selectedYear"}};o&&(null==(l=e.context.mainElement.querySelectorAll(s[n]))||l.forEach((e=>{e.removeAttribute(r[n].selected),e.removeAttribute(r[n].aria)})),p(e,r[n].selectedProperty,Number(t.dataset[r[n].value])),F(e),"year"===n&&I(e)),a&&(t.setAttribute(r[n].selected,""),t.setAttribute(r[n].aria,"true"))},_=(e,t)=>{var n;if("multiple"!==e.type)return{currentValue:null,columnID:0};const a=e.context.mainElement.querySelectorAll('[data-vc="column"]'),o=Array.from(a).findIndex((e=>e.closest(`[data-vc-column="${t}"]`)));return{currentValue:o>=0?Number(null==(n=a[o].querySelector(`[data-vc="${t}"]`))?void 0:n.getAttribute(`data-vc-${t}`)):null,columnID:Math.max(o,0)}},R=(e,t,n,a,o,l,s)=>{const r=t.cloneNode(!1);return r.className=e.styles.monthsMonth,r.innerText=a,r.ariaLabel=o,r.role="gridcell",r.dataset.vcMonthsMonth=`${s}`,l&&(r.ariaDisabled="true"),l&&(r.tabIndex=-1),r.disabled=l,O(e,r,"month",n===s,!1),r},K=(e,t)=>{var n,a;const o=null==(n=null==t?void 0:t.closest('[data-vc="header"]'))?void 0:n.querySelector('[data-vc="year"]'),l=o?Number(o.dataset.vcYear):e.context.selectedYear,s=(null==t?void 0:t.dataset.vcMonth)?Number(t.dataset.vcMonth):e.context.selectedMonth;p(e,"currentType","month"),q(e,t),F(e);const r=e.context.mainElement.querySelector('[data-vc="months"]');if(!e.selectionMonthsMode||!r)return;const i=e.monthsToSwitch>1?e.context.locale.months.long.map(((t,n)=>s-e.monthsToSwitch*n)).concat(e.context.locale.months.long.map(((t,n)=>s+e.monthsToSwitch*n))).filter((e=>e>=0&&e<=12)):Array.from(Array(12).keys()),c=document.createElement("button");c.type="button";for(let t=0;t<12;t++){const n=D(e.context.dateMin),a=D(e.context.dateMax),o=e.context.displayMonthsCount-1,{columnID:d}=_(e,"month"),u=l<=n.getFullYear()&&t<n.getMonth()+d||l>=a.getFullYear()&&t>a.getMonth()-o+d||l>a.getFullYear()||t!==s&&!i.includes(t),m=R(e,c,s,e.context.locale.months.short[t],e.context.locale.months.long[t],u,t);r.appendChild(m),e.onCreateMonthEls&&e.onCreateMonthEls(e,m)}null==(a=e.context.mainElement.querySelector("[data-vc-months-month]:not([disabled])"))||a.focus()},z=(e,t,n,a,o)=>`\n  <label class="${t}" data-vc-time-input="${e}">\n    <input type="text" name="${e}" maxlength="2" aria-label="${n[`input${e.charAt(0).toUpperCase()+e.slice(1)}`]}" value="${a}" ${o?"disabled":""}>\n  </label>\n`,j=(e,t,n,a,o,l,s)=>`\n  <label class="${t}" data-vc-time-range="${e}">\n    <input type="range" name="${e}" min="${a}" max="${o}" step="${l}" aria-label="${n[`range${e.charAt(0).toUpperCase()+e.slice(1)}`]}" value="${s}">\n  </label>\n`,U=(e,t,n,a)=>{({hour:()=>p(e,"selectedHours",n),minute:()=>p(e,"selectedMinutes",n)})[a](),p(e,"selectedTime",`${e.context.selectedHours}:${e.context.selectedMinutes}${e.context.selectedKeeping?` ${e.context.selectedKeeping}`:""}`),e.onChangeTime&&e.onChangeTime(e,t,!1),e.inputMode&&e.context.inputElement&&e.context.mainElement&&e.onChangeToInput&&e.onChangeToInput(e,t)},B=(e,t)=>{var n;return(null==(n={0:{AM:"00",PM:"12"},1:{AM:"01",PM:"13"},2:{AM:"02",PM:"14"},3:{AM:"03",PM:"15"},4:{AM:"04",PM:"16"},5:{AM:"05",PM:"17"},6:{AM:"06",PM:"18"},7:{AM:"07",PM:"19"},8:{AM:"08",PM:"20"},9:{AM:"09",PM:"21"},10:{AM:"10",PM:"22"},11:{AM:"11",PM:"23"},12:{AM:"00",PM:"12"}}[Number(e)])?void 0:n[t])||String(e)},Z=e=>({0:"12",13:"01",14:"02",15:"03",16:"04",17:"05",18:"06",19:"07",20:"08",21:"09",22:"10",23:"11"}[Number(e)]||String(e)),G=(e,t,n,a)=>{e.value=n,t.value=a},V=(e,t,n,a,o,l,s)=>{const r={hour:(r,i,c)=>{if(!e.selectionTimeMode)return;const d={12:()=>{if(!e.context.selectedKeeping)return;const d=Number(B(i,e.context.selectedKeeping));if(!(d<=l&&d>=s))return G(n,t,e.context.selectedHours,e.context.selectedHours),void(e.onChangeTime&&e.onChangeTime(e,c,!0));G(n,t,Z(i),B(i,e.context.selectedKeeping)),r>12&&((e,t,n)=>{t&&n&&(p(e,"selectedKeeping",n),t.innerText=n)})(e,a,"PM"),U(e,c,Z(i),o)},24:()=>{if(!(r<=l&&r>=s))return G(n,t,e.context.selectedHours,e.context.selectedHours),void(e.onChangeTime&&e.onChangeTime(e,c,!0));G(n,t,i,i),U(e,c,i,o)}};d[e.selectionTimeMode]()},minute:(a,r,i)=>{if(!(a<=l&&a>=s))return n.value=e.context.selectedMinutes,void(e.onChangeTime&&e.onChangeTime(e,i,!0));n.value=r,t.value=r,U(e,i,r,o)}},i=e=>{const t=Number(n.value),a=n.value.padStart(2,"0");r[o]&&r[o](t,a,e)};return n.addEventListener("change",i),()=>{n.removeEventListener("change",i)}},J=(e,t,n,a,o)=>{const l=l=>{const s=Number(t.value),r=t.value.padStart(2,"0"),i="hour"===o,c=24===e.selectionTimeMode,d=s>0&&s<12;i&&!c&&((e,t,n)=>{t&&(p(e,"selectedKeeping",n),t.innerText=n)})(e,a,0===s||d?"AM":"PM"),((e,t,n,a,o)=>{t.value=o,U(e,n,o,a)})(e,n,l,o,!i||c||d?r:Z(t.value))};return t.addEventListener("input",l),()=>{t.removeEventListener("input",l)}},X=e=>e.setAttribute("data-vc-input-focus",""),Q=e=>e.removeAttribute("data-vc-input-focus"),ee=(e,t)=>{const n=t.querySelector('[data-vc-time-range="hour"] input[name="hour"]'),a=t.querySelector('[data-vc-time-range="minute"] input[name="minute"]'),o=t.querySelector('[data-vc-time-input="hour"] input[name="hour"]'),l=t.querySelector('[data-vc-time-input="minute"] input[name="minute"]'),s=t.querySelector('[data-vc-time="keeping"]');if(!(n&&a&&o&&l))return;const r=e=>{e.target===n&&X(o),e.target===a&&X(l)},i=e=>{e.target===n&&Q(o),e.target===a&&Q(l)};return t.addEventListener("mouseover",r),t.addEventListener("mouseout",i),V(e,n,o,s,"hour",e.timeMaxHour,e.timeMinHour),V(e,a,l,s,"minute",e.timeMaxMinute,e.timeMinMinute),J(e,n,o,s,"hour"),J(e,a,l,s,"minute"),s&&((e,t,n,a,o)=>{const l=l=>{const s="AM"===e.context.selectedKeeping?"PM":"AM",r=B(e.context.selectedHours,s);Number(r)<=a&&Number(r)>=o?(p(e,"selectedKeeping",s),n.value=r,U(e,l,e.context.selectedHours,"hour"),t.ariaLabel=`${e.labels.btnKeeping} ${e.context.selectedKeeping}`,t.innerText=e.context.selectedKeeping):e.onChangeTime&&e.onChangeTime(e,l,!0)};t.addEventListener("click",l)})(e,s,n,e.timeMaxHour,e.timeMinHour),()=>{t.removeEventListener("mouseover",r),t.removeEventListener("mouseout",i)}},te=e=>{const t=e.selectedWeekends?[...e.selectedWeekends]:[],n=[...e.context.locale.weekdays.long].reduce(((n,a,o)=>[...n,{id:o,titleShort:e.context.locale.weekdays.short[o],titleLong:a,isWeekend:t.includes(o)}]),[]),a=[...n.slice(e.firstWeekday),...n.slice(0,e.firstWeekday)];e.context.mainElement.querySelectorAll('[data-vc="week"]').forEach((t=>{const n=e.onClickWeekDay?document.createElement("button"):document.createElement("b");e.onClickWeekDay&&(n.type="button"),a.forEach((a=>{const o=n.cloneNode(!0);o.innerText=a.titleShort,o.className=e.styles.weekDay,o.role="columnheader",o.ariaLabel=a.titleLong,o.dataset.vcWeekDay=String(a.id),a.isWeekend&&(o.dataset.vcWeekDayOff=""),t.appendChild(o)}))}))},ne=(e,t,n,a,o)=>{const l=t.cloneNode(!1);return l.className=e.styles.yearsYear,l.innerText=String(o),l.ariaLabel=String(o),l.role="gridcell",l.dataset.vcYearsYear=`${o}`,a&&(l.ariaDisabled="true"),a&&(l.tabIndex=-1),l.disabled=a,O(e,l,"year",n===o,!1),l},ae=(e,t)=>{var n;const a=(null==t?void 0:t.dataset.vcYear)?Number(t.dataset.vcYear):e.context.selectedYear;p(e,"currentType","year"),q(e,t),F(e),I(e);const o=e.context.mainElement.querySelector('[data-vc="years"]');if(!e.selectionYearsMode||!o)return;const l="multiple"!==e.type||e.context.selectedYear===a?0:1,s=document.createElement("button");s.type="button";for(let t=e.context.displayYear-7;t<e.context.displayYear+8;t++){const n=t<D(e.context.dateMin).getFullYear()+l||t>D(e.context.dateMax).getFullYear(),r=ne(e,s,a,n,t);o.appendChild(r),e.onCreateYearEls&&e.onCreateYearEls(e,r)}null==(n=e.context.mainElement.querySelector("[data-vc-years-year]:not([disabled])"))||n.focus()},oe={value:!1,set:()=>oe.value=!0,check:()=>oe.value},le=(e,t)=>e.dataset.vcTheme=t,se=(e,t)=>{if(le(e.context.mainElement,t.matches?"dark":"light"),"system"!==e.selectedTheme||oe.check())return;const n=e=>{const t=document.querySelectorAll('[data-vc="calendar"]');null==t||t.forEach((t=>le(t,e.matches?"dark":"light")))};t.addEventListener?t.addEventListener("change",n):t.addListener(n),oe.set()},re=(e,t)=>{const n=e.themeAttrDetect.length?document.querySelector(e.themeAttrDetect):null,a=e.themeAttrDetect.replace(/^.*\[(.+)\]/g,((e,t)=>t));if(!n||"system"===n.getAttribute(a))return void se(e,t);const o=n.getAttribute(a);o?(le(e.context.mainElement,o),((e,t,n)=>{new MutationObserver((e=>{for(let a=0;a<e.length;a++)if(e[a].attributeName===t){n();break}})).observe(e,{attributes:!0})})(n,a,(()=>{const t=n.getAttribute(a);t&&le(e.context.mainElement,t)}))):se(e,t)},ie=e=>e.charAt(0).toUpperCase()+e.slice(1).replace(/\./,""),ce=e=>{var t,n,a,o,l,s,r,c;if(!(e.context.locale.weekdays.short[6]&&e.context.locale.weekdays.long[6]&&e.context.locale.months.short[11]&&e.context.locale.months.long[11]))if("string"==typeof e.locale){if("string"==typeof e.locale&&!e.locale.length)throw new Error(m);Array.from({length:7},((t,n)=>((e,t,n)=>{const a=new Date(`1978-01-0${t+1}T00:00:00.000Z`),o=a.toLocaleString(n,{weekday:"short",timeZone:"UTC"}),l=a.toLocaleString(n,{weekday:"long",timeZone:"UTC"});e.context.locale.weekdays.short.push(ie(o)),e.context.locale.weekdays.long.push(ie(l))})(e,n,e.locale))),Array.from({length:12},((t,n)=>((e,t,n)=>{const a=new Date(`1978-${String(t+1).padStart(2,"0")}-01T00:00:00.000Z`),o=a.toLocaleString(n,{month:"short",timeZone:"UTC"}),l=a.toLocaleString(n,{month:"long",timeZone:"UTC"});e.context.locale.months.short.push(ie(o)),e.context.locale.months.long.push(ie(l))})(e,n,e.locale)))}else{if(!((null==(n=null==(t=e.locale)?void 0:t.weekdays)?void 0:n.short[6])&&(null==(o=null==(a=e.locale)?void 0:a.weekdays)?void 0:o.long[6])&&(null==(s=null==(l=e.locale)?void 0:l.months)?void 0:s.short[11])&&(null==(c=null==(r=e.locale)?void 0:r.months)?void 0:c.long[11])))throw new Error(m);p(e,"locale",i({},e.locale))}},de=e=>{const t={default:()=>{te(e),C(e)},multiple:()=>{te(e),C(e)},month:()=>K(e),year:()=>ae(e)};(e=>{"not all"!==window.matchMedia("(prefers-color-scheme)").media?"system"===e.selectedTheme?re(e,window.matchMedia("(prefers-color-scheme: dark)")):le(e.context.mainElement,e.selectedTheme):le(e.context.mainElement,"light")})(e),ce(e),q(e),F(e),I(e),(e=>{const t=e.context.mainElement.querySelector('[data-vc="time"]');if(!e.selectionTimeMode||!t)return;const[n,a]=[e.timeMinHour,e.timeMaxHour],[o,l]=[e.timeMinMinute,e.timeMaxMinute],s=e.context.selectedKeeping?B(e.context.selectedHours,e.context.selectedKeeping):e.context.selectedHours,r="range"===e.timeControls;var i;t.innerHTML=e.sanitizerHTML(`\n    <div class="${e.styles.timeContent}" data-vc-time="content">\n      ${z("hour",e.styles.timeHour,e.labels,e.context.selectedHours,r)}\n      ${z("minute",e.styles.timeMinute,e.labels,e.context.selectedMinutes,r)}\n      ${12===e.selectionTimeMode?(i=e.context.selectedKeeping,`<button type="button" class="${e.styles.timeKeeping}" aria-label="${e.labels.btnKeeping} ${i}" data-vc-time="keeping" ${r?"disabled":""}>${i}</button>`):""}\n    </div>\n    <div class="${e.styles.timeRanges}" data-vc-time="ranges">\n      ${j("hour",e.styles.timeRange,e.labels,n,a,e.timeStepHour,s)}\n      ${j("minute",e.styles.timeRange,e.labels,o,l,e.timeStepMinute,e.context.selectedMinutes)}\n    </div>\n  `),ee(e,t)})(e),t[e.context.currentType]()},ue=e=>{const t=t=>{var n;const a=t.target;if(!["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(t.key)||"button"!==a.localName)return;const o=Array.from(e.context.mainElement.querySelectorAll('[data-vc="calendar"] button')),l=o.indexOf(a);if(-1===l)return;const s=(r=o[l]).hasAttribute("data-vc-date-btn")?7:r.hasAttribute("data-vc-months-month")?4:r.hasAttribute("data-vc-years-year")?5:1;var r;const i=(0,{ArrowUp:()=>Math.max(0,l-s),ArrowDown:()=>Math.min(o.length-1,l+s),ArrowLeft:()=>Math.max(0,l-1),ArrowRight:()=>Math.min(o.length-1,l+1)}[t.key])();null==(n=o[i])||n.focus()};return e.context.mainElement.addEventListener("keydown",t),()=>e.context.mainElement.removeEventListener("keydown",t)},me=(e,t)=>{const n=D(E(new Date(e.context.selectedYear,e.context.selectedMonth,1)));({prev:()=>n.setMonth(n.getMonth()-e.monthsToSwitch),next:()=>n.setMonth(n.getMonth()+e.monthsToSwitch)})[t](),p(e,"selectedMonth",n.getMonth()),p(e,"selectedYear",n.getFullYear()),F(e),I(e),C(e)},he=e=>void 0===e.enableDateToggle||("function"==typeof e.enableDateToggle?e.enableDateToggle(e):e.enableDateToggle),ve=(e,t,n)=>{const a=t.dataset.vcDate,o=t.closest("[data-vc-date][data-vc-date-selected]"),l=he(e);if(o&&!l)return;const s=o?e.context.selectedDates.filter((e=>e!==a)):n?[...e.context.selectedDates,a]:[a];p(e,"selectedDates",s)},pe=(e,t,n)=>{if(!t)return;if(!n)return t.dataset.vcDateRangeTooltip="hidden",void(t.textContent="");const a=e.context.mainElement.getBoundingClientRect(),o=n.getBoundingClientRect();t.style.left=o.left-a.left+o.width/2+"px",t.style.top=o.bottom-a.top-o.height+"px",t.dataset.vcDateRangeTooltip="visible",t.innerHTML=e.sanitizerHTML(e.onCreateDateRangeTooltip(e,n,t,o,a))},ye={self:null,lastDateEl:null,isHovering:!1,rangeMin:void 0,rangeMax:void 0,tooltipEl:null,timeoutId:null},xe=(e,t,n)=>{var a,o,l;if(!(null==(o=null==(a=ye.self)?void 0:a.context)?void 0:o.selectedDates[0]))return;const s=E(e);(null==(l=ye.self.context.disableDates)?void 0:l.includes(s))||(ye.self.context.mainElement.querySelectorAll(`[data-vc-date="${s}"]`).forEach((e=>e.dataset.vcDateHover="")),t.forEach((e=>e.dataset.vcDateHover="first")),n.forEach((e=>{"first"===e.dataset.vcDateHover?e.dataset.vcDateHover="first-and-last":e.dataset.vcDateHover="last"})))},ge=()=>{var e,t;if(!(null==(t=null==(e=ye.self)?void 0:e.context)?void 0:t.mainElement))return;ye.self.context.mainElement.querySelectorAll("[data-vc-date-hover]").forEach((e=>e.removeAttribute("data-vc-date-hover")))},be=e=>t=>{ye.isHovering||(ye.isHovering=!0,requestAnimationFrame((()=>{e(t),ye.isHovering=!1})))},Me=be((e=>{var t,n;if(!e.target||!(null==(n=null==(t=ye.self)?void 0:t.context)?void 0:n.selectedDates[0]))return;if(!e.target.closest('[data-vc="dates"]'))return ye.lastDateEl=null,pe(ye.self,ye.tooltipEl,null),void ge();const a=e.target.closest("[data-vc-date]");if(!a||ye.lastDateEl===a)return;ye.lastDateEl=a,pe(ye.self,ye.tooltipEl,a),ge();const o=a.dataset.vcDate,l=D(ye.self.context.selectedDates[0]),s=D(o),r=ye.self.context.mainElement.querySelectorAll(`[data-vc-date="${ye.self.context.selectedDates[0]}"]`),i=ye.self.context.mainElement.querySelectorAll(`[data-vc-date="${o}"]`),[c,d]=l<s?[r,i]:[i,r],[u,m]=l<s?[l,s]:[s,l];for(let e=new Date(u);e<=m;e.setDate(e.getDate()+1))xe(e,c,d)})),fe=be((e=>{const t=e.target.closest("[data-vc-date-selected]");if(!t&&ye.lastDateEl)return ye.lastDateEl=null,void pe(ye.self,ye.tooltipEl,null);t&&ye.lastDateEl!==t&&(ye.lastDateEl=t,pe(ye.self,ye.tooltipEl,t))})),De=e=>{ye.self&&"Escape"===e.key&&(ye.lastDateEl=null,p(ye.self,"selectedDates",[]),ye.self.context.mainElement.removeEventListener("mousemove",Me),ye.self.context.mainElement.removeEventListener("keydown",De),pe(ye.self,ye.tooltipEl,null),ge())},Ee=()=>{null!==ye.timeoutId&&clearTimeout(ye.timeoutId),ye.timeoutId=setTimeout((()=>{ye.lastDateEl=null,pe(ye.self,ye.tooltipEl,null),ge()}),50)},we=(e,t)=>{ye.self=e,ye.lastDateEl=t,ge(),e.disableDatesGaps&&(ye.rangeMin=ye.rangeMin?ye.rangeMin:e.context.displayDateMin,ye.rangeMax=ye.rangeMax?ye.rangeMax:e.context.displayDateMax),e.onCreateDateRangeTooltip&&(ye.tooltipEl=e.context.mainElement.querySelector("[data-vc-date-range-tooltip]"));const n=null==t?void 0:t.dataset.vcDate;if(n){const t=1===e.context.selectedDates.length&&e.context.selectedDates[0].includes(n),a=t&&!he(e)?[n,n]:t&&he(e)?[]:e.context.selectedDates.length>1?[n]:[...e.context.selectedDates,n];p(e,"selectedDates",a),e.context.selectedDates.length>1&&e.context.selectedDates.sort(((e,t)=>+new Date(e)-+new Date(t)))}({set:()=>(e.disableDatesGaps&&(()=>{var e,t,n,a;if(!(null==(n=null==(t=null==(e=ye.self)?void 0:e.context)?void 0:t.selectedDates)?void 0:n[0])||!(null==(a=ye.self.context.disableDates)?void 0:a[0]))return;const o=D(ye.self.context.selectedDates[0]),[l,s]=ye.self.context.disableDates.map((e=>D(e))).reduce((([e,t],n)=>[o>=n?n:e,o<n&&null===t?n:t]),[null,null]);l&&p(ye.self,"displayDateMin",E(new Date(l.setDate(l.getDate()+1)))),s&&p(ye.self,"displayDateMax",E(new Date(s.setDate(s.getDate()-1)))),ye.self.disableDatesPast&&!ye.self.disableAllDates&&D(ye.self.context.displayDateMin)<D(ye.self.context.dateToday)&&p(ye.self,"displayDateMin",ye.self.context.dateToday)})(),pe(ye.self,ye.tooltipEl,t),ye.self.context.mainElement.removeEventListener("mousemove",fe),ye.self.context.mainElement.removeEventListener("mouseleave",Ee),ye.self.context.mainElement.removeEventListener("keydown",De),ye.self.context.mainElement.addEventListener("mousemove",Me),ye.self.context.mainElement.addEventListener("mouseleave",Ee),ye.self.context.mainElement.addEventListener("keydown",De),()=>{ye.self.context.mainElement.removeEventListener("mousemove",Me),ye.self.context.mainElement.removeEventListener("mouseleave",Ee),ye.self.context.mainElement.removeEventListener("keydown",De)}),reset:()=>{const[n,a]=[e.context.selectedDates[0],e.context.selectedDates[e.context.selectedDates.length-1]],o=e.context.selectedDates[0]!==e.context.selectedDates[e.context.selectedDates.length-1],l=w([`${n}:${a}`]).filter((t=>!e.context.disableDates.includes(t))),s=o?e.enableEdgeDatesOnly?[n,a]:l:[e.context.selectedDates[0],e.context.selectedDates[0]];if(p(e,"selectedDates",s),e.disableDatesGaps&&(p(e,"displayDateMin",ye.rangeMin),p(e,"displayDateMax",ye.rangeMax)),ye.self.context.mainElement.removeEventListener("mousemove",Me),ye.self.context.mainElement.removeEventListener("mouseleave",Ee),ye.self.context.mainElement.removeEventListener("keydown",De),e.onCreateDateRangeTooltip)return e.context.selectedDates[0]||(ye.self.context.mainElement.removeEventListener("mousemove",fe),ye.self.context.mainElement.removeEventListener("mouseleave",Ee),pe(ye.self,ye.tooltipEl,null)),e.context.selectedDates[0]&&(ye.self.context.mainElement.addEventListener("mousemove",fe),ye.self.context.mainElement.addEventListener("mouseleave",Ee),pe(ye.self,ye.tooltipEl,t)),()=>{ye.self.context.mainElement.removeEventListener("mousemove",fe),ye.self.context.mainElement.removeEventListener("mouseleave",Ee)}}})[1===e.context.selectedDates.length?"set":"reset"]()},Te=e=>{e.context.mainElement.querySelectorAll("[data-vc-date]").forEach((t=>{const n=t.querySelector("[data-vc-date-btn]"),a=t.dataset.vcDate,o=D(a).getDay();k(e,e.context.selectedYear,t,n,o,a,"current")}))},ke=["month","year"],$e=(e,t,n)=>{const{currentValue:a,columnID:o}=_(e,t);return"month"===e.context.currentType&&o>=0?n-o:"year"===e.context.currentType&&e.context.selectedYear!==a?n-1:n},Ae=(e,t,n,a)=>{var o;const l={year:()=>{if("multiple"===e.type)return((e,t)=>{const n=$e(e,"year",Number(t.dataset.vcYearsYear)),a=D(e.context.dateMin),o=D(e.context.dateMax),l=e.context.displayMonthsCount-1,{columnID:s}=_(e,"year"),r=e.context.selectedMonth<a.getMonth()&&n<=a.getFullYear(),i=e.context.selectedMonth>o.getMonth()-l+s&&n>=o.getFullYear(),c=n<a.getFullYear(),d=n>o.getFullYear(),u=r||c?a.getFullYear():i||d?o.getFullYear():n,m=r||c?a.getMonth():i||d?o.getMonth()-l+s:e.context.selectedMonth;p(e,"selectedYear",u),p(e,"selectedMonth",m)})(e,a);p(e,"selectedYear",Number(a.dataset.vcYearsYear))},month:()=>{if("multiple"===e.type)return((e,t)=>{const n=t.closest('[data-vc-column="month"]').querySelector('[data-vc="year"]'),a=$e(e,"month",Number(t.dataset.vcMonthsMonth)),o=Number(n.dataset.vcYear),l=D(e.context.dateMin),s=D(e.context.dateMax),r=a<l.getMonth()&&o<=l.getFullYear(),i=a>s.getMonth()&&o>=s.getFullYear();p(e,"selectedYear",o),p(e,"selectedMonth",r?l.getMonth():i?s.getMonth():a)})(e,a);p(e,"selectedMonth",Number(a.dataset.vcMonthsMonth))}};l[n]();({year:()=>{var n;return null==(n=e.onClickYear)?void 0:n.call(e,e,t)},month:()=>{var n;return null==(n=e.onClickMonth)?void 0:n.call(e,e,t)}})[n](),e.context.currentType!==e.type?(p(e,"currentType",e.type),de(e),null==(o=e.context.mainElement.querySelector(`[data-vc="${n}"]`))||o.focus()):O(e,a,n,!0,!0)},Ce=(e,t)=>{const n={month:e.selectionMonthsMode,year:e.selectionYearsMode};ke.forEach((a=>{n[a]&&t.target&&((e,t,n)=>{var a;const o=t.target,l=o.closest(`[data-vc="${n}"]`),s={year:()=>ae(e,o),month:()=>K(e,o)};if(l&&e.onClickTitle&&e.onClickTitle(e,t),l&&e.context.currentType!==n)return s[n]();const r=o.closest(`[data-vc-${n}s-${n}]`);if(r)return Ae(e,t,n,r);const i=o.closest('[data-vc="grid"]'),c=o.closest('[data-vc="column"]');(e.context.currentType===n&&l||"multiple"===e.type&&e.context.currentType===n&&i&&!c)&&(p(e,"currentType",e.type),de(e),null==(a=e.context.mainElement.querySelector(`[data-vc="${n}"]`))||a.focus())})(e,t,a)}))},Se=e=>{const t=t=>{((e,t)=>{const n=t.target.closest("[data-vc-arrow]");if(n){if(["default","multiple"].includes(e.context.currentType))me(e,n.dataset.vcArrow);else if("year"===e.context.currentType&&void 0!==e.context.displayYear){const a={prev:-15,next:15}[n.dataset.vcArrow];p(e,"displayYear",e.context.displayYear+a),ae(e,t.target)}e.onClickArrow&&e.onClickArrow(e,t)}})(e,t),((e,t)=>{if(!e.onClickWeekDay)return;const n=t.target.closest("[data-vc-week-day]"),a=t.target.closest('[data-vc="column"]'),o=a?a.querySelectorAll("[data-vc-date-week-day]"):e.context.mainElement.querySelectorAll("[data-vc-date-week-day]");if(!n||!o[0])return;const l=Number(n.dataset.vcWeekDay),s=Array.from(o).filter((e=>Number(e.dataset.vcDateWeekDay)===l));e.onClickWeekDay(e,l,s,t)})(e,t),((e,t)=>{if(!e.enableWeekNumbers||!e.onClickWeekNumber)return;const n=t.target.closest("[data-vc-week-number]"),a=e.context.mainElement.querySelectorAll("[data-vc-date-week-number]");if(!n||!a[0])return;const o=Number(n.innerText),l=Number(n.dataset.vcWeekYear),s=Array.from(a).filter((e=>Number(e.dataset.vcDateWeekNumber)===o));e.onClickWeekNumber(e,o,l,s,t)})(e,t),((e,t)=>{var n;const a=t.target,o=a.closest("[data-vc-date-btn]");if(!e.selectionDatesMode||!["single","multiple","multiple-ranged"].includes(e.selectionDatesMode)||!o)return;const l=o.closest("[data-vc-date]");({single:()=>ve(e,l,!1),multiple:()=>ve(e,l,!0),"multiple-ranged":()=>we(e,l)})[e.selectionDatesMode](),null==(n=e.context.selectedDates)||n.sort(((e,t)=>+new Date(e)-+new Date(t))),e.onClickDate&&e.onClickDate(e,t),e.inputMode&&e.context.inputElement&&e.context.mainElement&&e.onChangeToInput&&e.onChangeToInput(e,t);const s=a.closest('[data-vc-date-month="prev"]'),r=a.closest('[data-vc-date-month="next"]');({prev:()=>e.enableMonthChangeOnDayClick?me(e,"prev"):Te(e),next:()=>e.enableMonthChangeOnDayClick?me(e,"next"):Te(e),current:()=>Te(e)})[s?"prev":r?"next":"current"]()})(e,t),Ce(e,t)};return e.context.mainElement.addEventListener("click",t),()=>e.context.mainElement.removeEventListener("click",t)},Ye=(e,t)=>"today"===e?(()=>{const e=new Date;return new Date(e.getTime()-6e4*e.getTimezoneOffset()).toISOString().substring(0,10)})():e instanceof Date||"number"==typeof e||"string"==typeof e?w([e])[0]:t,Le=(e,t,n)=>{p(e,"selectedMonth",t),p(e,"selectedYear",n),p(e,"displayYear",n)},Ne=e=>{p(e,"currentType",e.type),(e=>{if("multiple"===e.type&&(e.displayMonthsCount<=1||e.displayMonthsCount>12))throw new Error(v);if("multiple"!==e.type&&e.displayMonthsCount>1)throw new Error(v);p(e,"displayMonthsCount",e.displayMonthsCount?e.displayMonthsCount:"multiple"===e.type?2:1)})(e),(e=>{var t,n,a;const o=Ye(e.dateMin,e.dateMin),l=Ye(e.dateMax,e.dateMax),s=Ye(e.displayDateMin,o),r=Ye(e.displayDateMax,l);p(e,"dateToday",Ye(e.dateToday,e.dateToday)),p(e,"displayDateMin",s?D(o)>=D(s)?o:s:o),p(e,"displayDateMax",r?D(l)<=D(r)?l:r:l);const i=e.disableDatesPast&&!e.disableAllDates&&D(s)<D(e.context.dateToday);p(e,"displayDateMin",i||e.disableAllDates?e.context.dateToday:s),p(e,"displayDateMax",e.disableAllDates?e.context.dateToday:r),p(e,"disableDates",e.disableDates[0]&&!e.disableAllDates?w(e.disableDates):e.disableAllDates?[e.context.displayDateMin]:[]),e.context.disableDates.length>1&&e.context.disableDates.sort(((e,t)=>+new Date(e)-+new Date(t))),p(e,"enableDates",e.enableDates[0]?w(e.enableDates):[]),(null==(t=e.context.enableDates)?void 0:t[0])&&(null==(n=e.context.disableDates)?void 0:n[0])&&p(e,"disableDates",e.context.disableDates.filter((t=>!e.context.enableDates.includes(t)))),e.context.enableDates.length>1&&e.context.enableDates.sort(((e,t)=>+new Date(e)-+new Date(t))),(null==(a=e.context.enableDates)?void 0:a[0])&&e.disableAllDates&&(p(e,"displayDateMin",e.context.enableDates[0]),p(e,"displayDateMax",e.context.enableDates[e.context.enableDates.length-1])),p(e,"dateMin",e.displayDisabledDates?o:e.context.displayDateMin),p(e,"dateMax",e.displayDisabledDates?l:e.context.displayDateMax)})(e),(e=>{var t;if(e.enableJumpToSelectedDate&&(null==(t=e.selectedDates)?void 0:t[0])&&void 0===e.selectedMonth&&void 0===e.selectedYear){const t=D(w(e.selectedDates)[0]);return void Le(e,t.getMonth(),t.getFullYear())}const n=void 0!==e.selectedMonth&&Number(e.selectedMonth)>=0&&Number(e.selectedMonth)<12,a=void 0!==e.selectedYear&&Number(e.selectedYear)>=0&&Number(e.selectedYear)<=9999;Le(e,n?Number(e.selectedMonth):D(e.context.dateToday).getMonth(),a?Number(e.selectedYear):D(e.context.dateToday).getFullYear())})(e),(e=>{var t;p(e,"selectedDates",(null==(t=e.selectedDates)?void 0:t[0])?w(e.selectedDates):[])})(e),(e=>{var t,n,a;if(!e.selectionTimeMode)return;if(![12,24].includes(e.selectionTimeMode))throw new Error(h);const o=12===e.selectionTimeMode,l=o?/^(0[1-9]|1[0-2]):([0-5][0-9]) ?(AM|PM)?$/i:/^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;let[s,r,i]=null!=(a=null==(n=null==(t=e.selectedTime)?void 0:t.match(l))?void 0:n.slice(1))?a:[];s?o&&!i&&(i="AM"):(s=o?Z(String(e.timeMinHour)):String(e.timeMinHour),r=String(e.timeMinMinute),i=o?Number(Z(String(e.timeMinHour)))>=12?"PM":"AM":null),p(e,"selectedHours",s.padStart(2,"0")),p(e,"selectedMinutes",r.padStart(2,"0")),p(e,"selectedKeeping",i),p(e,"selectedTime",`${e.context.selectedHours}:${e.context.selectedMinutes}${i?` ${i}`:""}`)})(e)},He=(e,{year:t,month:n,dates:a,time:o,locale:l},s=!0)=>{var r;const i={year:e.selectedYear,month:e.selectedMonth,dates:e.selectedDates,time:e.selectedTime};if(e.selectedYear=t?i.year:e.context.selectedYear,e.selectedMonth=n?i.month:e.context.selectedMonth,e.selectedTime=o?i.time:e.context.selectedTime,e.selectedDates="only-first"===a&&(null==(r=e.context.selectedDates)?void 0:r[0])?[e.context.selectedDates[0]]:!0===a?i.dates:e.context.selectedDates,l){p(e,"locale",{months:{short:[],long:[]},weekdays:{short:[],long:[]}})}Ne(e),s&&de(e),e.selectedYear=i.year,e.selectedMonth=i.month,e.selectedDates=i.dates,e.selectedTime=i.time,"multiple-ranged"===e.selectionDatesMode&&a&&we(e,null)},We=e=>{p(e,"inputElement",e.context.mainElement);const t=()=>{e.context.inputModeInit?queueMicrotask((()=>Fe(e))):(e=>{const t=document.createElement("div");t.className=e.styles.calendar,t.dataset.vc="calendar",t.dataset.vcInput="",t.dataset.vcCalendarHidden="",p(e,"inputModeInit",!0),p(e,"isShowInInputMode",!1),p(e,"mainElement",t),document.body.appendChild(e.context.mainElement),He(e,{year:!0,month:!0,dates:!0,time:!0,locale:!0}),queueMicrotask((()=>Fe(e))),e.onInit&&e.onInit(e),ue(e),Se(e)})(e)};return e.context.inputElement.addEventListener("click",t),e.context.inputElement.addEventListener("focus",t),()=>{e.context.inputElement.removeEventListener("click",t),e.context.inputElement.removeEventListener("focus",t)}},qe=(e,t)=>{if(!e.context.isInit)throw new Error(u);He(e,i(i({},{year:!0,month:!0,dates:!0,time:!0,locale:!0}),t),!(e.inputMode&&!e.context.inputModeInit)),e.onUpdate&&e.onUpdate(e)},Pe=(e,t)=>{const n=Object.keys(t);for(let a=0;a<n.length;a++){const o=n[a];"object"!=typeof e[o]||"object"!=typeof t[o]||t[o]instanceof Date||Array.isArray(t[o])?void 0!==t[o]&&(e[o]=t[o]):Pe(e[o],t[o])}};const Ie=(e,t,n)=>{if(!e)return;const a="auto"===n?function(e,t){const n="left";if(!t||!e)return n;const{canShow:a,parentPositions:o}=M(e,t),l=a.left&&a.right;return(l&&a.bottom?"center":l&&a.top?["top","center"]:Array.isArray(o)?["bottom"===o[0]?"top":"bottom",...o.slice(1)]:o)||n}(e,t):n,o={top:-t.offsetHeight,bottom:e.offsetHeight,left:0,center:e.offsetWidth/2-t.offsetWidth/2,right:e.offsetWidth-t.offsetWidth},l=Array.isArray(a)?a[0]:"bottom",s=Array.isArray(a)?a[1]:a;t.dataset.vcPosition=l;const{top:r,left:i}=x(e),c=r+o[l];let d=i+o[s];const{vw:u}=g();if(d+t.clientWidth>u){const e=window.innerWidth-document.body.clientWidth;d=u-t.clientWidth-e}else d<0&&(d=0);Object.assign(t.style,{left:`${d}px`,top:`${c}px`})},Fe=e=>{if(e.context.isShowInInputMode)return;if(!e.context.currentType)return void e.context.mainElement.click();p(e,"cleanupHandlers",[]),p(e,"isShowInInputMode",!0),Ie(e.context.inputElement,e.context.mainElement,e.positionToInput),e.context.mainElement.removeAttribute("data-vc-calendar-hidden");const t=()=>{Ie(e.context.inputElement,e.context.mainElement,e.positionToInput)};window.addEventListener("resize",t),e.context.cleanupHandlers.push((()=>window.removeEventListener("resize",t)));const n=t=>{"Escape"===t.key&&y(e)};document.addEventListener("keydown",n),e.context.cleanupHandlers.push((()=>document.removeEventListener("keydown",n)));const a=t=>{t.target===e.context.inputElement||e.context.mainElement.contains(t.target)||y(e)};document.addEventListener("click",a,{capture:!0}),e.context.cleanupHandlers.push((()=>document.removeEventListener("click",a,{capture:!0}))),e.onShow&&e.onShow(e)},Oe={application:"Calendar",navigation:"Calendar Navigation",arrowNext:{month:"Next month",year:"Next list of years"},arrowPrev:{month:"Previous month",year:"Previous list of years"},month:"Select month, current selected month:",months:"List of months",year:"Select year, current selected year:",years:"List of years",week:"Days of the week",weekNumber:"Numbers of weeks in a year",dates:"Dates in the current month",selectingTime:"Selecting a time ",inputHour:"Hours",inputMinute:"Minutes",rangeHour:"Slider for selecting hours",rangeMinute:"Slider for selecting minutes",btnKeeping:"Switch AM/PM, current position:"},_e={calendar:"vc",controls:"vc-controls",grid:"vc-grid",column:"vc-column",header:"vc-header",headerContent:"vc-header__content",month:"vc-month",year:"vc-year",arrowPrev:"vc-arrow vc-arrow_prev",arrowNext:"vc-arrow vc-arrow_next",wrapper:"vc-wrapper",content:"vc-content",months:"vc-months",monthsMonth:"vc-months__month",years:"vc-years",yearsYear:"vc-years__year",week:"vc-week",weekDay:"vc-week__day",weekNumbers:"vc-week-numbers",weekNumbersTitle:"vc-week-numbers__title",weekNumbersContent:"vc-week-numbers__content",weekNumber:"vc-week-number",dates:"vc-dates",date:"vc-date",dateBtn:"vc-date__btn",datePopup:"vc-date__popup",dateRangeTooltip:"vc-date-range-tooltip",time:"vc-time",timeContent:"vc-time__content",timeHour:"vc-time__hour",timeMinute:"vc-time__minute",timeKeeping:"vc-time__keeping",timeRanges:"vc-time__ranges",timeRange:"vc-time__range"};class Re{constructor(){c(this,"type","default"),c(this,"inputMode",!1),c(this,"positionToInput","left"),c(this,"firstWeekday",1),c(this,"monthsToSwitch",1),c(this,"themeAttrDetect","html[data-theme]"),c(this,"locale","en"),c(this,"dateToday","today"),c(this,"dateMin","1970-01-01"),c(this,"dateMax","2470-12-31"),c(this,"displayDateMin"),c(this,"displayDateMax"),c(this,"displayDatesOutside",!0),c(this,"displayDisabledDates",!1),c(this,"displayMonthsCount"),c(this,"disableDates",[]),c(this,"disableAllDates",!1),c(this,"disableDatesPast",!1),c(this,"disableDatesGaps",!1),c(this,"disableWeekdays",[]),c(this,"disableToday",!1),c(this,"enableDates",[]),c(this,"enableEdgeDatesOnly",!0),c(this,"enableDateToggle",!0),c(this,"enableWeekNumbers",!1),c(this,"enableMonthChangeOnDayClick",!0),c(this,"enableJumpToSelectedDate",!1),c(this,"selectionDatesMode","single"),c(this,"selectionMonthsMode",!0),c(this,"selectionYearsMode",!0),c(this,"selectionTimeMode",!1),c(this,"selectedDates",[]),c(this,"selectedMonth"),c(this,"selectedYear"),c(this,"selectedHolidays",[]),c(this,"selectedWeekends",[0,6]),c(this,"selectedTime"),c(this,"selectedTheme","system"),c(this,"timeMinHour",0),c(this,"timeMaxHour",23),c(this,"timeMinMinute",0),c(this,"timeMaxMinute",59),c(this,"timeControls","all"),c(this,"timeStepHour",1),c(this,"timeStepMinute",1),c(this,"sanitizerHTML",(e=>e)),c(this,"onClickDate"),c(this,"onClickWeekDay"),c(this,"onClickWeekNumber"),c(this,"onClickTitle"),c(this,"onClickMonth"),c(this,"onClickYear"),c(this,"onClickArrow"),c(this,"onChangeTime"),c(this,"onChangeToInput"),c(this,"onCreateDateRangeTooltip"),c(this,"onCreateDateEls"),c(this,"onCreateMonthEls"),c(this,"onCreateYearEls"),c(this,"onInit"),c(this,"onUpdate"),c(this,"onDestroy"),c(this,"onShow"),c(this,"onHide"),c(this,"popups",{}),c(this,"labels",i({},Oe)),c(this,"layouts",{default:"",multiple:"",month:"",year:""}),c(this,"styles",i({},_e))}}const Ke=class e extends Re{constructor(t,o){var l,s;super(),c(this,"init",(()=>{return p(e=this,"originalElement",e.context.mainElement.cloneNode(!0)),p(e,"isInit",!0),e.inputMode?We(e):(Ne(e),de(e),e.onInit&&e.onInit(e),ue(e),Se(e));var e})),c(this,"update",(e=>qe(this,e))),c(this,"destroy",(()=>(e=>{var t,n,a,o,l;if(!e.context.isInit)throw new Error(u);e.inputMode?(null==(t=e.context.mainElement.parentElement)||t.removeChild(e.context.mainElement),null==(a=null==(n=e.context.inputElement)?void 0:n.replaceWith)||a.call(n,e.context.originalElement),p(e,"inputElement",void 0)):null==(l=(o=e.context.mainElement).replaceWith)||l.call(o,e.context.originalElement),p(e,"mainElement",e.context.originalElement),e.onDestroy&&e.onDestroy(e)})(this))),c(this,"show",(()=>Fe(this))),c(this,"hide",(()=>y(this))),c(this,"set",((e,t)=>((e,t,n)=>{Pe(e,t),e.context.isInit&&qe(e,n)})(this,e,t))),c(this,"context"),this.context=(s=i({},this.context),n(s,a({locale:{months:{short:[],long:[]},weekdays:{short:[],long:[]}}}))),p(this,"mainElement","string"==typeof t?null!=(l=e.memoizedElements.get(t))?l:this.queryAndMemoize(t):t),o&&Pe(this,o)}queryAndMemoize(t){const n=document.querySelector(t);if(!n)throw new Error(d(t));return e.memoizedElements.set(t,n),n}};c(Ke,"memoizedElements",new Map);let ze=Ke;e.Calendar=ze,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}));