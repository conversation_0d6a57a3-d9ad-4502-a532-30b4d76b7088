/*! name: vanilla-calendar-pro v3.0.4 | url: https://github.com/uvarov-frontend/vanilla-calendar-pro */
const getDate$1=e=>new Date(`${e}T00:00:00`),getDateString$1=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,getWeekNumber$1=(e,t)=>{const a=getDate$1(e),g=(a.getDay()-t+7)%7;a.setDate(a.getDate()+4-g);const r=new Date(a.getFullYear(),0,1),D=Math.ceil(((+a-+r)/864e5+1)/7);return{year:a.getFullYear(),week:D}},parseDates$1=e=>e.reduce(((e,t)=>{if(t instanceof Date||"number"==typeof t){const a=t instanceof Date?t:new Date(t);e.push(a.toISOString().substring(0,10))}else t.match(/^(\d{4}-\d{2}-\d{2})$/g)?e.push(t):t.replace(/(\d{4}-\d{2}-\d{2}).*?(\d{4}-\d{2}-\d{2})/g,((t,a,g)=>{const r=getDate$1(a),D=getDate$1(g),n=new Date(r.getTime());for(;n<=D;n.setDate(n.getDate()+1))e.push(getDateString$1(n));return t}));return e}),[]),parseDates=e=>parseDates$1(e),getDateString=e=>getDateString$1(e),getDate=e=>getDate$1(e),getWeekNumber=(e,t)=>getWeekNumber$1(e,t);export{getDate,getDateString,getWeekNumber,parseDates};