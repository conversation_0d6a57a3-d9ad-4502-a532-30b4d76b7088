    -- Teen<PERSON>
    teenus_nimi VARCHAR(50) NOT NULL, -- elatise vastus, elatise nõustamine, elatise arvutamine, elatise dokument, elatise sissenõudmine, elatise <PERSON><PERSON><PERSON><PERSON>
    teenus_hind DECIMAL(10,2) DEFAULT NULL, -- hinnad on kirjas eraldi failis
    teenus_ettemaks DECIMAL(10,2) DEFAULT NULL, -- hinnad on kirjas eraldi failis
    teenus_kuupaev DATETIME NOT NULL, -- hinnad on kirjas eraldi failis
    teenus_kellaaeg DATETIME NOT NULL, -- hinnad on kirjas eraldi failis

    -- Klient
    klient_nimi VARCHAR(100) NOT NULL,
    klient_isikukood VARCHAR(20) DEFAULT NULL,
    klient_aadress VARCHAR(100) NOT NULL,
    klient_post VARCHAR(100) NOT NULL,
    klient_telefon VARCHAR(20) DEFAULT NULL,
    klient_lisainfo TEXT DEFAULT NULL,
    
    -- Ma<PERSON><PERSON>
    maksja_nimi VARCHAR(100) DEFAULT NULL,
    maksja_isikukood VARCHAR(20) DEFAULT NULL,
    maksja_aadress VARCHAR(100) NOT NULL,
    maksja_epost VARCHAR(100) NOT NULL,
    maksja_telefon VARCHAR(20) DEFAULT NULL,

    -- Lapsed
    lapsed_arv INT DEFAULT 1,
    lapsed_lapsetoetus VARCHAR(50) DEFAULT NULL,
    laps1_nimi VARCHAR(100) DEFAULT NULL,
    laps1_isikukood VARCHAR(20) DEFAULT NULL,
    laps2_oopaevad INT DEFAULT NULL,
    laps2_nimi VARCHAR(100) DEFAULT NULL,
    laps2_isikukood VARCHAR(20) DEFAULT NULL,
    laps2_oopaevad INT DEFAULT NULL,
    laps3_nimi VARCHAR(100) DEFAULT NULL,
    laps3_isikukood VARCHAR(20) DEFAULT NULL,
    laps3_oopaevad INT DEFAULT NULL,

    -- Elatisvõla andmed
    elatisvolg_noudmine VARCHAR(50) DEFAULT NULL,
    elatisvolg_kuupaev DECIMAL(10,2) DEFAULT NULL,
    elatisvolg_makstud DECIMAL(10,2) DEFAULT NULL,

    -- Üldandmed
    muu_lisainfo TEXT DEFAULT NULL,
    muu_aeg TEXT DEFAULT NULL,
    muu_kasutustingimused BOOLEAN NOT NULL DEFAULT 0,
    muu_dokumendid VARCHAR(255) DEFAULT NULL,
    muu_kuupaev DATETIME NOT NULL,
    muu_loodud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Muu_uuendatud TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better performance
CREATE INDEX idx_kliendid_teenus ON kliendid(teenus);
CREATE INDEX idx_kliendid_isikukood ON kliendid(isikukood);
CREATE INDEX idx_kliendid_email ON kliendid(email);
CREATE INDEX idx_kliendid_submission_date ON kliendid(submission_date);

-- Elatise nõudmise teenuse tabel (säilitatud tagasiühilduvuse jaoks)
CREATE TABLE IF NOT EXISTS elatise_noudmine (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- Kliendi andmed
    klient_eesnimi VARCHAR(100) NOT NULL,
    klient_perenimi VARCHAR(100) NOT NULL,
    klient_isikukood VARCHAR(20) NOT NULL,
    klient_email VARCHAR(100) NOT NULL,
    klient_telefon VARCHAR(20) NOT NULL,
    klient_arvelduskonto VARCHAR(50) NOT NULL,

    -- Lapse andmed
    laps_eesnimi VARCHAR(100) NOT NULL,
    laps_perenimi VARCHAR(100) NOT NULL,
    laps_isikukood VARCHAR(20) NOT NULL,
    laps_oopaevad INT,

    -- Mitme lapse tugi
    laps_arv INT DEFAULT 1,
    laps2_eesnimi VARCHAR(100),
    laps2_perenimi VARCHAR(100),
    laps2_isikukood VARCHAR(20),
    laps2_oopaevad INT,

    laps3_eesnimi VARCHAR(100),
    laps3_perenimi VARCHAR(100),
    laps3_isikukood VARCHAR(20),
    laps3_oopaevad INT,

    -- Maksja andmed
    maksja_eesnimi VARCHAR(100) NOT NULL,
    maksja_perenimi VARCHAR(100) NOT NULL,
    maksja_isikukood VARCHAR(20) NOT NULL,
    maksja_aadress VARCHAR(255),
    maksja_telefon VARCHAR(20),
    maksja_email VARCHAR(100),

    -- Lapsetoetuse andmed
    lapsetoetus VARCHAR(50) DEFAULT NULL,

    -- Elatisvõla andmed
    elatisvolg VARCHAR(50) DEFAULT NULL,
    elatisvolg_summa DECIMAL(10,2),
    elatisvolg_makstud DECIMAL(10,2),
    elatisvolg_kuupaev VARCHAR(20) DEFAULT NULL,

    -- Paketi andmed
    pakett VARCHAR(50) DEFAULT 'standard',
    paketi_hind DECIMAL(10,2),

    -- Lisainfo
    lisainfo TEXT,

    -- Üldandmed
    tingimused BOOLEAN NOT NULL DEFAULT 0,
    dokumendid_path VARCHAR(255),
    submission_date DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better performance
CREATE INDEX idx_elatise_noudmine_klient_isikukood ON elatise_noudmine(klient_isikukood);
CREATE INDEX idx_elatise_noudmine_laps_isikukood ON elatise_noudmine(laps_isikukood);
CREATE INDEX idx_elatise_noudmine_maksja_isikukood ON elatise_noudmine(maksja_isikukood);
CREATE INDEX idx_elatise_noudmine_submission_date ON elatise_noudmine(submission_date);
